/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background: #ffffff;
  --surface: #f8fafc;
  --surface-dark: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --border: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --radius: 8px;
  --radius-lg: 12px;
  --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;

  /* Code block colors for light theme */
  --code-bg: #f6f8fa;
  --code-border: #e1e4e8;
  --code-text: #24292e;
  --inline-code-bg: #f3f4f6;
  --inline-code-text: #e11d48;
  --inline-code-border: #e5e7eb;
}

/* Dark theme variables */
[data-theme="dark"] {
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --secondary-color: #94a3b8;
  --success-color: #22c55e;
  --warning-color: #fbbf24;
  --error-color: #f87171;
  --background: #0f172a;
  --surface: #1e293b;
  --surface-dark: #334155;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;
  --border: #334155;
  --border-light: #475569;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);

  /* Code block colors for dark theme */
  --code-bg: #1e293b;
  --code-border: #475569;
  --code-text: #e2e8f0;
  --inline-code-bg: #334155;
  --inline-code-text: #fbbf24;
  --inline-code-border: #475569;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Selection colors */
::selection {
  background: var(--primary-color);
  color: white;
}

/* Scrollbar styling for dark theme */
[data-theme="dark"] ::-webkit-scrollbar {
  width: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--surface);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--border-light);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Header */
.header {
  background: var(--background);
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  flex-wrap: wrap;
  gap: 1rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.version {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
}

.base-url {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--surface);
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  border: 1px solid var(--border);
}

.base-url .label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.base-url .url {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  color: var(--primary-color);
  background: var(--background);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Theme Toggle Button */
.theme-toggle {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1.25rem;
}

.theme-toggle:hover {
  background: var(--surface-dark);
  transform: scale(1.05);
}

.theme-toggle:active {
  transform: scale(0.95);
}

.theme-toggle:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Search Input */
.search-input {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  background: var(--background);
  color: var(--text-primary);
  min-width: 200px;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* Layout */
#app {
  display: grid;
  grid-template-columns: 280px 1fr;
  grid-template-rows: auto 1fr;
  min-height: 100vh;
}

.header {
  grid-column: 1 / -1;
}

/* Sidebar Navigation */
.sidebar {
  background: var(--surface);
  border-right: 1px solid var(--border);
  overflow-y: auto;
  position: sticky;
  top: 73px;
  height: calc(100vh - 73px);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.nav-content {
  padding: 1.5rem 1rem;
}

.nav-section {
  margin-bottom: 2rem;
}

.nav-section h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.75rem;
}

.nav-section ul {
  list-style: none;
}

.nav-section li {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: block;
  padding: 0.5rem 0.75rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.nav-link:hover {
  background: var(--background);
  color: var(--text-primary);
}

.nav-link.active {
  background: var(--primary-color);
  color: white;
  font-weight: 500;
}

/* Main Content */
.main-content {
  padding: 2rem 0;
  overflow-x: hidden;
}

.section {
  margin-bottom: 4rem;
  scroll-margin-top: 100px;
}

.section h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  border-bottom: 2px solid var(--border);
  padding-bottom: 0.5rem;
}

.section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 2rem 0 1rem 0;
}

.section h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 1.5rem 0 0.75rem 0;
}

.section p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.7;
}

/* Feature Grid */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.feature-card {
  background: var(--surface);
  padding: 1.5rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
  transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.3s ease, border-color 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.feature-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.feature-card p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Code Blocks */
.code-block {
  position: relative;
  margin: 1rem 0;
}

.code-block pre {
  background: var(--code-bg) !important;
  border: 1px solid var(--code-border) !important;
  border-radius: var(--radius);
  padding: 1rem;
  overflow-x: auto;
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--code-text) !important;
}

.code-block code {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  color: var(--code-text) !important;
  background: transparent !important;
}

/* Theme-aware code elements - Higher specificity */
.code-block pre[class*="language-"],
pre[class*="language-"] {
  background: var(--code-bg) !important;
  border: 1px solid var(--code-border) !important;
  color: var(--code-text) !important;
}

.code-block code[class*="language-"],
code[class*="language-"] {
  color: var(--code-text) !important;
  background: transparent !important;
}

/* Force theme variables for all code elements */
[data-theme="dark"] .code-block pre,
[data-theme="dark"] .code-block code,
[data-theme="dark"] pre[class*="language-"],
[data-theme="dark"] code[class*="language-"] {
  background: var(--code-bg) !important;
  color: var(--code-text) !important;
  border-color: var(--code-border) !important;
}

/* Inline code styling */
:not(pre) > code {
  background: var(--inline-code-bg) !important;
  color: var(--inline-code-text) !important;
  border: 1px solid var(--inline-code-border) !important;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: var(--font-mono);
  font-size: 0.875rem;
}

/* Additional dark theme enforcement for code blocks */
[data-theme="dark"] .code-block,
[data-theme="dark"] .code-block pre,
[data-theme="dark"] .code-block code {
  background: var(--code-bg) !important;
  color: var(--code-text) !important;
  border-color: var(--code-border) !important;
}

[data-theme="dark"] :not(pre) > code {
  background: var(--inline-code-bg) !important;
  color: var(--inline-code-text) !important;
  border-color: var(--inline-code-border) !important;
}

/* Copy Button */
.copy-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
}

.copy-button:hover {
  background: var(--surface);
}

.copy-button.copied {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

/* Rate Limits */
.rate-limits {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
}

.rate-limit-card {
  background: var(--surface);
  padding: 1rem;
  border-radius: var(--radius);
  border: 1px solid var(--border);
  text-align: center;
}

.rate-limit-card h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.rate-limit-card p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Error Table */
.error-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  background: var(--background);
  border-radius: var(--radius);
  overflow: hidden;
  border: 1px solid var(--border);
}

.error-table th,
.error-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border);
}

.error-table th {
  background: var(--surface);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.error-table td {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.error-table code {
  background: var(--surface);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: var(--font-mono);
  font-size: 0.8rem;
  color: var(--error-color);
}

/* Steps */
.steps {
  margin: 2rem 0;
}

.step {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
}

.step-number {
  flex-shrink: 0;
  width: 2rem;
  height: 2rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.step-content p {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
}

/* Tabs */
.example-tabs {
  margin: 2rem 0;
}

.tab-buttons {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--border);
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: var(--text-primary);
  background: var(--surface);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  position: relative;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* SDK Grid */
.sdk-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.sdk-card {
  background: var(--surface);
  padding: 1.5rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
}

.sdk-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.sdk-card p {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Service Sections */
.service-section {
  border-top: 3px solid var(--primary-color);
  padding-top: 2rem;
  margin-bottom: 3rem;
}

.service-description {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.service-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.info-card {
  background: var(--surface);
  padding: 1rem;
  border-radius: var(--radius);
  border: 1px solid var(--border);
  text-align: center;
}

.info-card h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-card code {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  color: var(--primary-color);
  background: var(--background);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* Endpoints */
.endpoints-container {
  margin-top: 2rem;
}

.endpoint {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  margin-bottom: 2rem;
  overflow: hidden;
  transition: box-shadow 0.2s ease;
}

.endpoint:hover {
  box-shadow: var(--shadow-lg);
}

.endpoint-header {
  background: var(--surface);
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.method {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-family: var(--font-mono);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.method-get {
  background: #dcfdf7;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.method-post {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

.method-put {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fcd34d;
}

.method-delete {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* Dark theme method badges */
[data-theme="dark"] .method-get {
  background: #064e3b;
  color: #6ee7b7;
  border: 1px solid #047857;
}

[data-theme="dark"] .method-post {
  background: #1e3a8a;
  color: #93c5fd;
  border: 1px solid #3b82f6;
}

[data-theme="dark"] .method-put {
  background: #92400e;
  color: #fcd34d;
  border: 1px solid #d97706;
}

[data-theme="dark"] .method-delete {
  background: #7f1d1d;
  color: #fca5a5;
  border: 1px solid #dc2626;
}

.path {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  color: var(--text-primary);
  background: var(--background);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--border-light);
  flex: 1;
  min-width: 200px;
}

.endpoint-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

.endpoint-content {
  padding: 1.5rem;
}

.endpoint-description {
  font-size: 1rem;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* Auth and Rate Limit Badges */
.auth-required,
.rate-limit {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1rem;
  margin: 1rem 0;
}

.auth-badge,
.rate-badge {
  display: inline-block;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.auth-badge {
  background: #fef3c7;
  color: #92400e;
}

.rate-badge {
  background: #e0e7ff;
  color: #3730a3;
}

/* Dark theme badges */
[data-theme="dark"] .auth-badge {
  background: #92400e;
  color: #fcd34d;
}

[data-theme="dark"] .rate-badge {
  background: #3730a3;
  color: #c7d2fe;
}

/* Parameters Table */
.params-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  background: var(--background);
  border-radius: var(--radius);
  overflow: hidden;
  border: 1px solid var(--border);
}

.params-table th,
.params-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border);
}

.params-table th {
  background: var(--surface);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.params-table td {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.param-type {
  background: var(--surface);
  color: var(--primary-color);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: var(--font-mono);
  font-size: 0.75rem;
  font-weight: 500;
}

.param-required {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.param-required.required {
  background: #fee2e2;
  color: #991b1b;
}

.param-required.optional {
  background: #f3f4f6;
  color: #6b7280;
}

/* Dark theme parameter badges */
[data-theme="dark"] .param-required.required {
  background: #7f1d1d;
  color: #fca5a5;
}

[data-theme="dark"] .param-required.optional {
  background: #374151;
  color: #9ca3af;
}

/* Request/Response Sections */
.request-section,
.response-section,
.example-section,
.parameters-section {
  margin: 2rem 0;
}

.request-section h4,
.response-section h4,
.example-section h4,
.parameters-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  #app {
    grid-template-columns: 1fr;
  }

  .sidebar {
    display: none;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .base-url {
    justify-content: center;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .rate-limits {
    grid-template-columns: 1fr;
  }

  .sdk-grid {
    grid-template-columns: 1fr;
  }

  .step {
    flex-direction: column;
    text-align: center;
  }

  .step-number {
    align-self: center;
  }

  .endpoint-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .path {
    min-width: auto;
  }

  .service-info {
    grid-template-columns: 1fr;
  }

  .params-table {
    font-size: 0.75rem;
  }

  .params-table th,
  .params-table td {
    padding: 0.5rem;
  }
}
